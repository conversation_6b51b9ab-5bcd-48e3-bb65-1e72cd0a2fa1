hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@8.0.0':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.23.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/react-slick@1.1.2(react@19.1.0)':
    '@ant-design/react-slick': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.1':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.27.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.1':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.2.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/trigger': private
  '@rc-component/util@1.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/util': private
  '@rolldown/pluginutils@1.0.0-beta.9':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0)(typescript@5.8.3))(eslint@9.27.0)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.32.1(eslint@9.27.0)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.32.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.32.1(eslint@9.27.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.32.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.32.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.32.1(eslint@9.27.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.32.1':
    '@typescript-eslint/visitor-keys': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.5:
    browserslist: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  classnames@2.5.1:
    classnames: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  electron-to-chromium@1.5.158:
    electron-to-chromium: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fsevents@2.3.3:
    fsevents: private
  gensync@1.0.0-beta.2:
    gensync: private
  glob-parent@6.0.2:
    glob-parent: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lru-cache@5.1.1:
    lru-cache: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.3:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  rc-cascader@3.34.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-dialog: private
  rc-drawer@7.2.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-field-form: private
  rc-image@7.12.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-image: private
  rc-input-number@9.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-input-number: private
  rc-input@1.8.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-input: private
  rc-mentions@2.20.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-menu: private
  rc-motion@2.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-motion: private
  rc-notification@5.6.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-picker: private
  rc-progress@4.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-progress: private
  rc-rate@2.13.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-segmented: private
  rc-select@14.16.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-select: private
  rc-slider@11.1.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-slider: private
  rc-steps@6.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-steps: private
  rc-switch@4.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-switch: private
  rc-table@7.50.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-table: private
  rc-tabs@15.6.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tabs: private
  rc-textarea@1.10.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tree: private
  rc-upload@4.9.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-upload: private
  rc-util@5.44.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-util: private
  rc-virtual-list@3.18.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-virtual-list: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rollup@4.41.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.26.0:
    scheduler: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  source-map-js@1.2.1:
    source-map-js: private
  string-convert@0.2.1:
    string-convert: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stylis@4.3.6:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  yallist@3.1.1:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Tue, 27 May 2025 03:30:59 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\cnErrorApp\error-book-app\node_modules\.pnpm
virtualStoreDirMaxLength: 60
