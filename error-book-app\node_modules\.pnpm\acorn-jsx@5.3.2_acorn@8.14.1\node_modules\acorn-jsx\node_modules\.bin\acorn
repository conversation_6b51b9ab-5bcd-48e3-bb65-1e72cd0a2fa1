#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/acorn@8.14.1/node_modules/acorn/bin/node_modules:/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/acorn@8.14.1/node_modules/acorn/node_modules:/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/acorn@8.14.1/node_modules:/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/acorn@8.14.1/node_modules/acorn/bin/node_modules:/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/acorn@8.14.1/node_modules/acorn/node_modules:/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/acorn@8.14.1/node_modules:/mnt/c/Users/<USER>/Desktop/cnErrorApp/error-book-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../acorn@8.14.1/node_modules/acorn/bin/acorn" "$@"
else
  exec node  "$basedir/../../../../../acorn@8.14.1/node_modules/acorn/bin/acorn" "$@"
fi
