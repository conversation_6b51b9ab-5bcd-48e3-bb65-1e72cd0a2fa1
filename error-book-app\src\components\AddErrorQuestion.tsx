import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  message,
  Space,
  Tag,
  Divider
} from 'antd'
import {
  SaveOutlined,
  CloseOutlined,
  PlusOutlined,
  BookOutlined
} from '@ant-design/icons'

const { TextArea } = Input
const { Option } = Select

interface AddErrorQuestionProps {
  onCancel: () => void
  onSave: (questionData: any) => void
}

const AddErrorQuestion: React.FC<AddErrorQuestionProps> = ({ onCancel, onSave }) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [tags, setTags] = useState<string[]>([])
  const [inputTag, setInputTag] = useState('')

  const subjects = [
    { value: 'math', label: '数学' },
    { value: 'chinese', label: '语文' },
    { value: 'english', label: '英语' },
    { value: 'physics', label: '物理' },
    { value: 'chemistry', label: '化学' },
    { value: 'other', label: '其他学科' }
  ]

  const difficulties = [
    { value: 'easy', label: '简单' },
    { value: 'medium', label: '中等' },
    { value: 'hard', label: '困难' }
  ]

  const handleAddTag = () => {
    if (inputTag && !tags.includes(inputTag)) {
      setTags([...tags, inputTag])
      setInputTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      const questionData = {
        ...values,
        tags,
        createdAt: new Date(),
        status: 'new'
      }

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      onSave(questionData)
      message.success('错题添加成功！')
      form.resetFields()
      setTags([])
    } catch (error) {
      message.error('添加失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="main-content-container">
      <div className="main-content-wrapper">
        {/* 页面标题 */}
        <div style={{ marginBottom: '32px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            marginBottom: '6px'
          }}>
            <BookOutlined style={{ fontSize: '18px', color: '#ff6b35' }} />
            <h1 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            }}>
              添加错题
            </h1>
          </div>
          <p style={{
            fontSize: '14px',
            color: '#8c8c8c',
            margin: 0,
            paddingLeft: '28px',
            fontWeight: '400'
          }}>
            记录和分析你的错题，让学习更有针对性
          </p>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          requiredMark={false}
        >
          {/* 基本信息卡片 */}
          <Card
            title="基本信息"
            style={{
              borderRadius: '16px',
              border: 'none',
              boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
              marginBottom: '24px'
            }}
            styles={{ body: { padding: '32px' } }}
          >
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px', marginBottom: '24px' }}>
              <Form.Item
                name="subject"
                label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>学科</span>}
                rules={[{ required: true, message: '请选择学科' }]}
              >
                <Select
                  placeholder="选择学科"
                  size="large"
                  style={{ borderRadius: '8px' }}
                >
                  {subjects.map(subject => (
                    <Option key={subject.value} value={subject.value}>
                      {subject.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="difficulty"
                label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>难度</span>}
                rules={[{ required: true, message: '请选择难度' }]}
              >
                <Select
                  placeholder="选择难度"
                  size="large"
                  style={{ borderRadius: '8px' }}
                >
                  {difficulties.map(difficulty => (
                    <Option key={difficulty.value} value={difficulty.value}>
                      {difficulty.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <Form.Item
              name="title"
              label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>题目标题</span>}
              rules={[{ required: true, message: '请输入题目标题' }]}
            >
              <Input
                placeholder="请输入题目标题"
                size="large"
                style={{ borderRadius: '8px' }}
              />
            </Form.Item>

            <Form.Item
              name="source"
              label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>题目来源</span>}
            >
              <Input
                placeholder="例如：教材第3章练习题、期中考试第5题等（可选）"
                size="large"
                style={{ borderRadius: '8px' }}
              />
            </Form.Item>
          </Card>

          {/* 题目内容卡片 */}
          <Card
            title="题目内容"
            style={{
              borderRadius: '16px',
              border: 'none',
              boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
              marginBottom: '24px'
            }}
            styles={{ body: { padding: '32px' } }}
          >
            <Form.Item
              name="question"
              label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>题目描述</span>}
              rules={[{ required: true, message: '请输入题目描述' }]}
            >
              <TextArea
                placeholder="请详细描述题目内容..."
                rows={6}
                style={{ borderRadius: '8px', fontSize: '15px', lineHeight: '1.6' }}
              />
            </Form.Item>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
              <Form.Item
                name="correctAnswer"
                label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>正确答案</span>}
                rules={[{ required: true, message: '请输入正确答案' }]}
              >
                <TextArea
                  placeholder="请输入正确答案..."
                  rows={4}
                  style={{ borderRadius: '8px' }}
                />
              </Form.Item>

              <Form.Item
                name="myAnswer"
                label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>我的错误答案</span>}
                rules={[{ required: true, message: '请输入你的错误答案' }]}
              >
                <TextArea
                  placeholder="请输入你当时的错误答案..."
                  rows={4}
                  style={{ borderRadius: '8px' }}
                />
              </Form.Item>
            </div>
          </Card>

          {/* 错误分析卡片 */}
          <Card
            title="错误分析"
            style={{
              borderRadius: '16px',
              border: 'none',
              boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
              marginBottom: '24px'
            }}
            styles={{ body: { padding: '32px' } }}
          >
            <Form.Item
              name="errorAnalysis"
              label={<span style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>错误原因分析</span>}
              rules={[{ required: true, message: '请分析错误原因' }]}
            >
              <TextArea
                placeholder="分析一下为什么会做错这道题，是概念不清、计算错误、还是其他原因..."
                rows={5}
                style={{ borderRadius: '8px', fontSize: '15px', lineHeight: '1.6' }}
              />
            </Form.Item>

            {/* 标签输入 */}
            <div style={{ marginTop: '24px' }}>
              <label style={{ fontSize: '14px', fontWeight: '500', color: '#262626', marginBottom: '12px', display: 'block' }}>
                知识点标签
              </label>
              <div style={{ marginBottom: '16px' }}>
                <Space size={8} wrap>
                  {tags.map(tag => (
                    <Tag
                      key={tag}
                      closable
                      onClose={() => handleRemoveTag(tag)}
                      style={{
                        borderRadius: '6px',
                        padding: '4px 8px',
                        fontSize: '13px'
                      }}
                    >
                      {tag}
                    </Tag>
                  ))}
                </Space>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <Input
                  placeholder="输入知识点标签，如：二次函数、语法、化学方程式等"
                  value={inputTag}
                  onChange={(e) => setInputTag(e.target.value)}
                  onPressEnter={handleAddTag}
                  style={{ borderRadius: '8px' }}
                />
                <Button
                  type="dashed"
                  icon={<PlusOutlined />}
                  onClick={handleAddTag}
                  style={{ borderRadius: '8px' }}
                >
                  添加
                </Button>
              </div>
            </div>
          </Card>

          {/* 操作按钮 */}
          <Card
            style={{
              borderRadius: '16px',
              border: 'none',
              boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
              marginBottom: '40px'
            }}
            styles={{ body: { padding: '32px' } }}
          >
            <div style={{ display: 'flex', justifyContent: 'center', gap: '16px' }}>
              <Button
                size="large"
                onClick={onCancel}
                style={{
                  borderRadius: '8px',
                  padding: '12px 32px',
                  height: 'auto',
                  fontSize: '16px',
                  fontWeight: '500'
                }}
              >
                <CloseOutlined style={{ marginRight: '8px' }} />
                取消
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                style={{
                  background: '#ff6b35',
                  borderColor: '#ff6b35',
                  borderRadius: '8px',
                  padding: '12px 32px',
                  height: 'auto',
                  fontSize: '16px',
                  fontWeight: '500'
                }}
              >
                <SaveOutlined style={{ marginRight: '8px' }} />
                {loading ? '保存中...' : '保存错题'}
              </Button>
            </div>
          </Card>
        </Form>

        {/* Footer Section */}
        <div style={{
          padding: '30px 0',
          marginTop: '40px'
        }}>
          <div style={{
            maxWidth: '1200px',
            margin: '0 auto',
            padding: '0 20px'
          }}>
            <div style={{
              background: '#fafafa',
              borderRadius: '8px',
              border: '1px solid #f0f0f0',
              padding: '20px 32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              fontSize: '15px',
              color: '#999999'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
                <span
                  style={{
                    cursor: 'pointer',
                    fontSize: '15px',
                    color: '#666666',
                    transition: 'color 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#1890ff'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#666666'}
                >
                  关于错题本
                </span>
                <span
                  style={{
                    cursor: 'pointer',
                    fontSize: '15px',
                    color: '#666666',
                    transition: 'color 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#1890ff'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#666666'}
                >
                  版权说明和法律声明
                </span>
                <span
                  style={{
                    cursor: 'pointer',
                    fontSize: '15px',
                    color: '#666666',
                    transition: 'color 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#1890ff'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#666666'}
                >
                  隐私政策
                </span>
              </div>
              <div style={{
                fontSize: '15px',
                color: '#999999',
                fontWeight: '400'
              }}>
                © 1998-2025 Tencent Inc. All Rights Reserved.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AddErrorQuestion
