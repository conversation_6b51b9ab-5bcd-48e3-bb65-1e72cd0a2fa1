import React from 'react'
import { Layout } from 'antd'
import Sidebar from './components/Sidebar'
import MainContent from './components/MainContent'
import './App.css'

const { Sider, Content } = Layout

function App() {
  return (
    <div style={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Fixed Sidebar */}
      <div
        style={{
          width: '280px',
          height: '100vh',
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
          position: 'fixed',
          left: 0,
          top: 0,
          zIndex: 100
        }}
      >
        <Sidebar />
      </div>

      {/* Main Content Area */}
      <div
        style={{
          marginLeft: '280px',
          width: 'calc(100% - 280px)',
          height: '100vh',
          overflow: 'auto'
        }}
      >
        <MainContent />
      </div>
    </div>
  )
}

export default App
