// 错题数据类型
export interface ErrorQuestion {
  id: string
  subject: string        // 学科
  chapter: string        // 章节
  title: string          // 题目标题
  question: string       // 题目内容
  answer: string         // 正确答案
  myAnswer: string       // 我的答案
  explanation: string    // 解析
  difficulty: 'easy' | 'medium' | 'hard'  // 难度
  tags: string[]         // 知识点标签
  masteryLevel: number   // 掌握程度 0-100
  reviewCount: number    // 复习次数
  lastReviewDate: Date   // 最后复习时间
  nextReviewDate: Date   // 下次复习时间
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
  images?: string[]      // 题目图片
  status: 'new' | 'reviewing' | 'mastered' | 'difficult'  // 状态
}

// 学科类型
export interface Subject {
  id: string
  name: string
  color: string
  icon: string
  questionCount: number
}

// 统计数据类型
export interface Statistics {
  totalQuestions: number
  newQuestions: number
  reviewingQuestions: number
  masteredQuestions: number
  difficultQuestions: number
  todayAdded: number
  todayReviewed: number
  weeklyProgress: number[]
  subjectDistribution: { subject: string; count: number }[]
}

// 复习计划类型
export interface ReviewPlan {
  id: string
  questionId: string
  scheduledDate: Date
  completed: boolean
  completedAt?: Date
  difficulty: 'easy' | 'medium' | 'hard'
}

// 通知类型
export interface Notification {
  id: string
  type: 'review' | 'achievement' | 'system'
  title: string
  content: string
  read: boolean
  createdAt: Date
}

// 用户信息类型
export interface UserInfo {
  id: string
  name: string
  avatar?: string
  level: number
  experience: number
  studyDays: number
  achievements: string[]
}

// 菜单项类型
export interface MenuItem {
  key: string
  label: string
  icon?: React.ReactNode
  children?: MenuItem[]
}

// 应用状态类型
export interface AppState {
  currentPage: string
  selectedSubject?: string
  searchKeyword: string
  filterOptions: {
    difficulty?: 'easy' | 'medium' | 'hard'
    status?: 'new' | 'reviewing' | 'mastered' | 'difficult'
    subject?: string
    dateRange?: [Date, Date]
  }
}
