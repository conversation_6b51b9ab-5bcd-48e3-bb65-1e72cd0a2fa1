/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 覆盖antd默认样式 */
.ant-layout {
  background: #fff !important;
}

.ant-layout-sider {
  background: #fff !important;
}

.ant-menu {
  background: transparent !important;
  border: none !important;
}

.ant-menu-item {
  border-radius: 10px !important;
  margin: 10px 0px !important;
  padding: 20px 16px !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.ant-menu-item-selected {
  background-color: #FFF2E8 !important;
  color: #ff6b35 !important;
}

.ant-menu-item:hover {
  background-color: #f5f5f5 !important;
}

.ant-menu-submenu-title {
  border-radius: 10px !important;
  margin: 6px 0px !important;
  padding: 15px 16px !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.ant-menu-submenu-title:hover {
  background-color: #f5f5f5 !important;
}

/* 当子菜单项被选中时，父菜单的样式 */
.ant-menu-submenu-selected .ant-menu-submenu-title {
  background-color: #ffffff !important;
  color: #333333 !important;
}

/* 当子菜单展开且有子项被选中时，父菜单的样式 */
.ant-menu-submenu-open.ant-menu-submenu-selected .ant-menu-submenu-title {
  background-color: #ffffff !important;
  color: #333333 !important;
}

/* 子菜单箭头样式 */
.ant-menu-submenu-arrow {
  color: #999 !important;
  font-size: 12px !important;
}

.ant-menu-submenu-open .ant-menu-submenu-arrow {
  transform: rotate(180deg) !important;
}

.ant-menu-sub {
  background: transparent !important;
  padding: 2px 0 6px 0 !important;
}

.ant-menu-sub .ant-menu-item {
  margin: 2px 0px !important;
  padding: 24px 16px 24px 40px !important;
  height: 42px !important;
  line-height: 36px !important;
  font-size: 15px !important;
  border-radius: 10px !important;
}

.ant-menu-sub .ant-menu-item:hover {
  background-color: #f8f8f8 !important;
  color: #333 !important;
}

.ant-menu-sub .ant-menu-item-selected {
  background-color: #FFF2E8 !important;
  color: #ff6b35 !important;
  font-weight: 600 !important;
}

/* 卡片样式 */
.ant-card {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02) !important;
  border: 1px solid #f0f0f0 !important;
}

.ant-card:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05) !important;
  transition: box-shadow 0.3s ease !important;
}

/* 按钮样式 */
.ant-btn-primary {
  background: #ff6b35 !important;
  border-color: #ff6b35 !important;
}

.ant-btn-primary:hover {
  background: #ff8c5a !important;
  border-color: #ff8c5a !important;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px !important;
  font-size: 12px !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 主内容区响应式布局 */
.main-content-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
  display: flex;
  justify-content: center;
}

.main-content-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content-container {
    padding: 16px;
  }

  .main-content-wrapper {
    padding: 0 12px;
  }
}

@media (max-width: 480px) {
  .main-content-container {
    padding: 12px;
  }

  .main-content-wrapper {
    padding: 0 8px;
  }
}
