import React, { useState } from 'react'
import { Modal, Form, Input, Select, Tag, Button, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useErrorBookStore } from '../store'

const { TextArea } = Input
const { Option } = Select

interface AddQuestionProps {
  visible: boolean
  onClose: () => void
}

const AddQuestion: React.FC<AddQuestionProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm()
  const [tags, setTags] = useState<string[]>([])
  const [inputTag, setInputTag] = useState('')
  const { addQuestion } = useErrorBookStore()

  const subjects = ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理', '政治']
  const difficulties = [
    { value: 'easy', label: '简单' },
    { value: 'medium', label: '中等' },
    { value: 'hard', label: '困难' }
  ]

  const handleAddTag = () => {
    if (inputTag && !tags.includes(inputTag)) {
      setTags([...tags, inputTag])
      setInputTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSubmit = async (values: any) => {
    try {
      const questionData = {
        ...values,
        tags,
        chapter: values.chapter || '未分类'
      }
      
      addQuestion(questionData)
      message.success('错题添加成功！')
      form.resetFields()
      setTags([])
      onClose()
    } catch (error) {
      message.error('添加失败，请重试')
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setTags([])
    onClose()
  }

  return (
    <Modal
      title="添加错题"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ marginTop: '20px' }}
      >
        <Form.Item
          name="subject"
          label="学科"
          rules={[{ required: true, message: '请选择学科' }]}
        >
          <Select placeholder="选择学科">
            {subjects.map(subject => (
              <Option key={subject} value={subject}>
                {subject}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="chapter"
          label="章节"
        >
          <Input placeholder="请输入章节名称（可选）" />
        </Form.Item>

        <Form.Item
          name="title"
          label="题目标题"
          rules={[{ required: true, message: '请输入题目标题' }]}
        >
          <Input placeholder="请输入题目标题" />
        </Form.Item>

        <Form.Item
          name="question"
          label="题目内容"
          rules={[{ required: true, message: '请输入题目内容' }]}
        >
          <TextArea 
            rows={4} 
            placeholder="请输入完整的题目内容"
          />
        </Form.Item>

        <Form.Item
          name="myAnswer"
          label="我的答案"
          rules={[{ required: true, message: '请输入你的答案' }]}
        >
          <TextArea 
            rows={2} 
            placeholder="请输入你当时的答案"
          />
        </Form.Item>

        <Form.Item
          name="answer"
          label="正确答案"
          rules={[{ required: true, message: '请输入正确答案' }]}
        >
          <TextArea 
            rows={2} 
            placeholder="请输入正确答案"
          />
        </Form.Item>

        <Form.Item
          name="explanation"
          label="解析"
          rules={[{ required: true, message: '请输入解析' }]}
        >
          <TextArea 
            rows={3} 
            placeholder="请输入详细的解题思路和解析"
          />
        </Form.Item>

        <Form.Item
          name="difficulty"
          label="难度"
          rules={[{ required: true, message: '请选择难度' }]}
        >
          <Select placeholder="选择难度">
            {difficulties.map(diff => (
              <Option key={diff.value} value={diff.value}>
                {diff.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="知识点标签">
          <div style={{ marginBottom: '8px' }}>
            {tags.map(tag => (
              <Tag
                key={tag}
                closable
                onClose={() => handleRemoveTag(tag)}
                style={{ marginBottom: '4px' }}
              >
                {tag}
              </Tag>
            ))}
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Input
              value={inputTag}
              onChange={(e) => setInputTag(e.target.value)}
              onPressEnter={handleAddTag}
              placeholder="输入知识点标签"
              style={{ flex: 1 }}
            />
            <Button 
              type="dashed" 
              onClick={handleAddTag}
              icon={<PlusOutlined />}
            >
              添加
            </Button>
          </div>
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button onClick={handleCancel} style={{ marginRight: '8px' }}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            添加错题
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AddQuestion
