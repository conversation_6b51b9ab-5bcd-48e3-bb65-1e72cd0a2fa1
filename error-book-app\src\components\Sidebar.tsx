import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'antd'
import {
  HomeOutlined,
  EditOutlined,
  BookOutlined,
  SyncOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  RightOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useErrorBookStore } from '../store'

const Sidebar: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('home')
  const [openKeys, setOpenKeys] = useState<string[]>([])
  const { getUnreadNotifications, userInfo } = useErrorBookStore()

  const unreadCount = getUnreadNotifications().length

  const menuItems: MenuProps['items'] = [
    {
      key: 'home',
      icon: <HomeOutlined style={{ fontSize: '18px' }} />,
      label: '首页'
    },
    {
      key: 'error-management',
      icon: <EditOutlined style={{ fontSize: '18px' }} />,
      label: '错题管理',
      children: [
        { key: 'add-error', label: '添加错题' },
        { key: 'error-list', label: '错题列表' },
        { key: 'error-category', label: '错题分类' }
      ]
    },
    {
      key: 'subject-management',
      icon: <BookOutlined style={{ fontSize: '18px' }} />,
      label: '学科管理',
      children: [
        { key: 'math', label: '数学' },
        { key: 'chinese', label: '语文' },
        { key: 'english', label: '英语' },
        { key: 'physics', label: '物理' },
        { key: 'chemistry', label: '化学' },
        { key: 'other', label: '其他学科' }
      ]
    },
    {
      key: 'review-plan',
      icon: <SyncOutlined style={{ fontSize: '18px' }} />,
      label: '复习计划',
      children: [
        { key: 'today-review', label: '今日复习' },
        { key: 'review-calendar', label: '复习日历' },
        { key: 'review-settings', label: '复习设置' }
      ]
    },
    {
      key: 'data-center',
      icon: <BarChartOutlined style={{ fontSize: '18px' }} />,
      label: '数据中心',
      children: [
        { key: 'data-overview', label: '数据概览' },
        { key: 'progress-analysis', label: '进步分析' },
        { key: 'study-report', label: '学习报告' }
      ]
    },
    {
      key: 'settings',
      icon: <SettingOutlined style={{ fontSize: '18px' }} />,
      label: '设置',
      children: [
        { key: 'personal-info', label: '个人信息' },
        { key: 'system-settings', label: '系统设置' },
        { key: 'data-management', label: '数据管理' }
      ]
    }
  ]

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setSelectedKey(e.key)
  }

  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys)
  }

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* Logo区域 - 固定顶部 */}
      <div style={{
        padding: '40px 20px 20px 20px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        flexShrink: 0,
        background: '#fff',
        zIndex: 10
      }}>
        <div style={{
          width: '28px',
          height: '28px',
          background: '#ff6b35',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          transform: 'rotate(45deg)'
        }}>
          <div style={{ transform: 'rotate(-45deg)' }}>M</div>
        </div>
        <span style={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#333'
        }}>
          错题本·助手
        </span>
      </div>

      {/* 导航菜单 - 可滚动中间区域 */}
      <div
        className="sidebar-menu-scroll"
        style={{
          flex: 1,
          padding: '20px',
          overflow: 'auto'
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          openKeys={openKeys}
          onClick={handleMenuClick}
          onOpenChange={handleOpenChange}
          items={menuItems}
          style={{
            border: 'none',
            background: 'transparent'
          }}
        />
      </div>

      {/* 底部功能区 - 固定底部 */}
      <div style={{
        padding: '20px',
        borderTop: '1px solid #f0f0f0',
        flexShrink: 0,
        background: '#fff',
        zIndex: 10
      }}>
        {/* 通知中心 */}
        <div style={{
          padding: '14px 16px',
          cursor: 'pointer',
          borderRadius: '10px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '14px',
          marginBottom: '10px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Badge count={unreadCount} size="small">
            <BellOutlined style={{ fontSize: '18px', color: '#666' }} />
          </Badge>
          <span style={{ color: '#333', fontSize: '16px' }}>通知中心</span>
        </div>

        {/* 用户信息 */}
        <div style={{
          padding: '14px 16px',
          cursor: 'pointer',
          borderRadius: '10px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '14px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Avatar size={24} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <span style={{ color: '#1890ff', fontSize: '16px', flex: 1 }}>{userInfo.name}</span>
          <RightOutlined style={{ fontSize: '14px', color: '#999' }} />
        </div>
      </div>
    </div>
  )
}

export default Sidebar
