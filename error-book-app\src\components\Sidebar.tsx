import React, { useState } from 'react'
import { <PERSON>u, <PERSON>tar, Badge } from 'antd'
import {
  HomeOutlined,
  EditOutlined,
  BookOutlined,
  SyncOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  RightOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useErrorBookStore } from '../store'

const Sidebar: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('home')
  const { getUnreadNotifications, userInfo } = useErrorBookStore()

  const unreadCount = getUnreadNotifications().length

  const menuItems: MenuProps['items'] = [
    {
      key: 'home',
      icon: <HomeOutlined style={{ fontSize: '16px' }} />,
      label: '首页',
      style: {
        backgroundColor: selectedKey === 'home' ? '#fff2e8' : 'transparent',
        color: selectedKey === 'home' ? '#ff6b35' : '#333',
        borderRadius: '8px',
        margin: '4px 16px',
        height: '44px',
        lineHeight: '44px',
        fontSize: '14px'
      }
    },
    {
      key: 'error-management',
      icon: <EditOutlined style={{ fontSize: '16px' }} />,
      label: '错题管理',
      style: {
        borderRadius: '8px',
        margin: '4px 16px',
        height: '44px',
        lineHeight: '44px',
        fontSize: '14px'
      }
    },
    {
      key: 'subject-management',
      icon: <BookOutlined style={{ fontSize: '16px' }} />,
      label: '学科管理',
      style: {
        borderRadius: '8px',
        margin: '4px 16px',
        height: '44px',
        lineHeight: '44px',
        fontSize: '14px'
      }
    },
    {
      key: 'review-plan',
      icon: <SyncOutlined style={{ fontSize: '16px' }} />,
      label: '复习计划',
      style: {
        borderRadius: '8px',
        margin: '4px 16px',
        height: '44px',
        lineHeight: '44px',
        fontSize: '14px'
      }
    },
    {
      key: 'data-center',
      icon: <BarChartOutlined style={{ fontSize: '16px' }} />,
      label: '数据中心',
      style: {
        borderRadius: '8px',
        margin: '4px 16px',
        height: '44px',
        lineHeight: '44px',
        fontSize: '14px'
      }
    },
    {
      key: 'settings',
      icon: <SettingOutlined style={{ fontSize: '16px' }} />,
      label: '设置',
      style: {
        borderRadius: '8px',
        margin: '4px 16px',
        height: '44px',
        lineHeight: '44px',
        fontSize: '14px'
      }
    }
  ]

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setSelectedKey(e.key)
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo区域 */}
      <div style={{
        padding: '20px 16px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        gap: '10px'
      }}>
        <div style={{
          width: '24px',
          height: '24px',
          background: '#ff6b35',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          transform: 'rotate(45deg)'
        }}>
          <div style={{ transform: 'rotate(-45deg)' }}>M</div>
        </div>
        <span style={{
          fontSize: '16px',
          fontWeight: '500',
          color: '#333'
        }}>
          错题本·助手
        </span>
      </div>

      {/* 导航菜单 */}
      <div style={{ flex: 1, padding: '16px 5p' }}>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={handleMenuClick}
          items={menuItems}
          style={{
            border: 'none',
            background: 'transparent'
          }}
        />
      </div>

      {/* 底部功能区 */}
      <div style={{
        padding: '16px',
        borderTop: '1px solid #f0f0f0'
      }}>
        {/* 通知中心 */}
        <div style={{
          padding: '12px 16px',
          cursor: 'pointer',
          borderRadius: '8px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '8px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Badge count={unreadCount} size="small">
            <BellOutlined style={{ fontSize: '16px', color: '#666' }} />
          </Badge>
          <span style={{ color: '#333', fontSize: '14px' }}>通知中心</span>
        </div>

        {/* 用户信息 */}
        <div style={{
          padding: '12px 16px',
          cursor: 'pointer',
          borderRadius: '8px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Avatar size={20} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <span style={{ color: '#1890ff', fontSize: '14px', flex: 1 }}>{userInfo.name}</span>
          <RightOutlined style={{ fontSize: '12px', color: '#999' }} />
        </div>
      </div>
    </div>
  )
}

export default Sidebar
