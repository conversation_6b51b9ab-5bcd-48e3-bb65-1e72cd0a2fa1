import React, { useState } from 'react'
import { <PERSON>u, <PERSON><PERSON>, Badge } from 'antd'
import {
  HomeOutlined,
  EditOutlined,
  BookOutlined,
  SyncOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  RightOutlined,
  DownOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useErrorBookStore } from '../store'

const Sidebar: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('home')
  const { getUnreadNotifications, userInfo } = useErrorBookStore()

  const unreadCount = getUnreadNotifications().length

  const menuItems: MenuProps['items'] = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
      style: {
        backgroundColor: selectedKey === 'home' ? '#fff2e8' : 'transparent',
        color: selectedKey === 'home' ? '#ff6b35' : '#333',
        borderRadius: '6px',
        margin: '2px 12px',
        height: '36px',
        lineHeight: '36px'
      }
    },
    {
      key: 'error-management',
      icon: <EditOutlined />,
      label: '错题管理',
      style: {
        borderRadius: '6px',
        margin: '2px 12px',
        height: '36px',
        lineHeight: '36px'
      }
    },
    {
      key: 'subject-management',
      icon: <BookOutlined />,
      label: '学科管理',
      style: {
        borderRadius: '6px',
        margin: '2px 12px',
        height: '36px',
        lineHeight: '36px'
      }
    },
    {
      key: 'review-plan',
      icon: <SyncOutlined />,
      label: '复习计划',
      style: {
        borderRadius: '6px',
        margin: '2px 12px',
        height: '36px',
        lineHeight: '36px'
      }
    },
    {
      key: 'data-center',
      icon: <BarChartOutlined />,
      label: '数据中心',
      style: {
        borderRadius: '6px',
        margin: '2px 12px',
        height: '36px',
        lineHeight: '36px'
      }
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      style: {
        borderRadius: '6px',
        margin: '2px 12px',
        height: '36px',
        lineHeight: '36px'
      }
    }
  ]

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setSelectedKey(e.key)
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo区域 */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <div style={{
          width: '20px',
          height: '20px',
          background: '#ff6b35',
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '12px',
          fontWeight: 'bold',
          transform: 'rotate(45deg)'
        }}>
          <div style={{ transform: 'rotate(-45deg)' }}>M</div>
        </div>
        <span style={{
          fontSize: '14px',
          fontWeight: '500',
          color: '#333'
        }}>
          错题本·助手
        </span>
      </div>

      {/* 导航菜单 */}
      <div style={{ flex: 1, padding: '8px 0' }}>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={handleMenuClick}
          items={menuItems}
          style={{
            border: 'none',
            background: 'transparent'
          }}
        />
      </div>

      {/* 底部功能区 */}
      <div style={{
        padding: '12px',
        borderTop: '1px solid #f0f0f0'
      }}>
        {/* 通知中心 */}
        <div style={{
          padding: '8px 12px',
          cursor: 'pointer',
          borderRadius: '6px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '4px',
          fontSize: '14px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Badge count={unreadCount} size="small">
            <BellOutlined style={{ fontSize: '14px', color: '#666' }} />
          </Badge>
          <span style={{ color: '#333', fontSize: '13px' }}>通知中心</span>
        </div>

        {/* 用户信息 */}
        <div style={{
          padding: '8px 12px',
          cursor: 'pointer',
          borderRadius: '6px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Avatar size={16} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <span style={{ color: '#1890ff', fontSize: '13px', flex: 1 }}>{userInfo.name}</span>
          <RightOutlined style={{ fontSize: '10px', color: '#999' }} />
        </div>
      </div>
    </div>
  )
}

export default Sidebar
