import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'antd'
import {
  HomeOutlined,
  EditOutlined,
  BookOutlined,
  SyncOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  RightOutlined,
  DownOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useErrorBookStore } from '../store'

const Sidebar: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('home')
  const { getUnreadNotifications, userInfo } = useErrorBookStore()

  const unreadCount = getUnreadNotifications().length

  const menuItems: MenuProps['items'] = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
      style: {
        backgroundColor: selectedKey === 'home' ? '#fff2e8' : 'transparent',
        color: selectedKey === 'home' ? '#ff6b35' : '#333',
        borderRadius: '8px',
        margin: '4px 8px'
      }
    },
    {
      key: 'error-management',
      icon: <EditOutlined />,
      label: '错题管理',
      children: [
        { key: 'add-error', label: '添加错题' },
        { key: 'error-list', label: '错题列表' },
        { key: 'error-category', label: '错题分类' }
      ]
    },
    {
      key: 'subject-management',
      icon: <BookOutlined />,
      label: '学科管理',
      children: [
        { key: 'math', label: '数学' },
        { key: 'chinese', label: '语文' },
        { key: 'english', label: '英语' },
        { key: 'other', label: '其他学科' }
      ]
    },
    {
      key: 'review-plan',
      icon: <SyncOutlined />,
      label: '复习计划',
      children: [
        { key: 'today-review', label: '今日复习' },
        { key: 'review-calendar', label: '复习日历' },
        { key: 'review-settings', label: '复习设置' }
      ]
    },
    {
      key: 'statistics',
      icon: <BarChartOutlined />,
      label: '学习统计',
      children: [
        { key: 'data-overview', label: '数据概览' },
        { key: 'progress-analysis', label: '进步分析' },
        { key: 'study-report', label: '学习报告' }
      ]
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      children: [
        { key: 'personal-info', label: '个人信息' },
        { key: 'system-settings', label: '系统设置' },
        { key: 'data-management', label: '数据管理' }
      ]
    }
  ]

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setSelectedKey(e.key)
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo区域 */}
      <div style={{
        padding: '20px 16px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <div style={{
          width: '24px',
          height: '24px',
          background: '#ff6b35',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold'
        }}>
          📖
        </div>
        <span style={{
          fontSize: '16px',
          fontWeight: '500',
          color: '#333'
        }}>
          错题本·助手
        </span>
      </div>

      {/* 导航菜单 */}
      <div style={{ flex: 1, padding: '16px 0' }}>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={handleMenuClick}
          items={menuItems}
          style={{
            border: 'none',
            background: 'transparent'
          }}
        />
      </div>

      {/* 底部功能区 */}
      <div style={{
        padding: '16px',
        borderTop: '1px solid #f0f0f0'
      }}>
        {/* 通知中心 */}
        <div style={{
          padding: '12px 16px',
          cursor: 'pointer',
          borderRadius: '8px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '8px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Badge count={unreadCount} size="small">
            <BellOutlined style={{ fontSize: '16px', color: '#666' }} />
          </Badge>
          <span style={{ color: '#333', fontSize: '14px' }}>通知中心</span>
        </div>

        {/* 用户信息 */}
        <div style={{
          padding: '12px 16px',
          cursor: 'pointer',
          borderRadius: '8px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Avatar size={20} icon={<UserOutlined />} />
          <span style={{ color: '#1890ff', fontSize: '14px' }}>{userInfo.name}</span>
          <RightOutlined style={{ fontSize: '12px', color: '#999', marginLeft: 'auto' }} />
        </div>
      </div>
    </div>
  )
}

export default Sidebar
