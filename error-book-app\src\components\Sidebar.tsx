import React, { useState } from 'react'
import { <PERSON>u, <PERSON><PERSON>, Badge } from 'antd'
import {
  HomeOutlined,
  EditOutlined,
  BookOutlined,
  SyncOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  RightOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useErrorBookStore } from '../store'

const Sidebar: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('home')
  const { getUnreadNotifications, userInfo } = useErrorBookStore()

  const unreadCount = getUnreadNotifications().length

  const menuItems: MenuProps['items'] = [
    {
      key: 'home',
      icon: <HomeOutlined style={{ fontSize: '18px' }} />,
      label: '首页',
      style: {
        backgroundColor: selectedKey === 'home' ? '#fff2e8' : 'transparent',
        color: selectedKey === 'home' ? '#ff6b35' : '#333',
        borderRadius: '10px',
        margin: '6px 0px',
        padding: '16px 16px',
        height: '48px',
        lineHeight: '48px',
        fontSize: '16px'
      }
    },
    {
      key: 'error-management',
      icon: <EditOutlined style={{ fontSize: '18px' }} />,
      label: '错题管理',
      style: {
        borderRadius: '10px',
        margin: '6px 0px',
        padding: '15px 16px',
        height: '48px',
        lineHeight: '48px',
        fontSize: '16px'
      }
    },
    {
      key: 'subject-management',
      icon: <BookOutlined style={{ fontSize: '18px' }} />,
      label: '学科管理',
      style: {
        borderRadius: '10px',
        margin: '6px 0px',
        padding: '15px 16px',
        height: '48px',
        lineHeight: '48px',
        fontSize: '16px'
      }
    },
    {
      key: 'review-plan',
      icon: <SyncOutlined style={{ fontSize: '18px' }} />,
      label: '复习计划',
      style: {
        borderRadius: '10px',
        margin: '6px 0px',
        padding: '15px 16px',
        height: '48px',
        lineHeight: '48px',
        fontSize: '16px'
      }
    },
    {
      key: 'data-center',
      icon: <BarChartOutlined style={{ fontSize: '18px' }} />,
      label: '数据中心',
      style: {
        borderRadius: '10px',
        margin: '6px 0px',
        padding: '15px 16px',
        height: '48px',
        lineHeight: '48px',
        fontSize: '16px'
      }
    },
    {
      key: 'settings',
      icon: <SettingOutlined style={{ fontSize: '18px' }} />,
      label: '设置',
      style: {
        borderRadius: '10px',
        margin: '6px 0px',
        padding: '15px 16px',
        height: '48px',
        lineHeight: '48px',
        fontSize: '16px'
      }
    }
  ]

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setSelectedKey(e.key)
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo区域 */}
      <div style={{
        padding: '40px 20px 20px 20px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}>
        <div style={{
          width: '28px',
          height: '28px',
          background: '#ff6b35',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold',
          transform: 'rotate(45deg)'
        }}>
          <div style={{ transform: 'rotate(-45deg)' }}>M</div>
        </div>
        <span style={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#333'
        }}>
          错题本·助手
        </span>
      </div>

      {/* 导航菜单 */}
      <div style={{ flex: 1, padding: '20px' }}>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={handleMenuClick}
          items={menuItems}
          style={{
            border: 'none',
            background: 'transparent'
          }}
        />
      </div>

      {/* 底部功能区 */}
      <div style={{
        padding: '20px',
        borderTop: '1px solid #f0f0f0'
      }}>
        {/* 通知中心 */}
        <div style={{
          padding: '14px 16px',
          cursor: 'pointer',
          borderRadius: '10px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '14px',
          marginBottom: '10px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Badge count={unreadCount} size="small">
            <BellOutlined style={{ fontSize: '18px', color: '#666' }} />
          </Badge>
          <span style={{ color: '#333', fontSize: '16px' }}>通知中心</span>
        </div>

        {/* 用户信息 */}
        <div style={{
          padding: '14px 16px',
          cursor: 'pointer',
          borderRadius: '10px',
          transition: 'background-color 0.2s',
          display: 'flex',
          alignItems: 'center',
          gap: '14px'
        }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
        >
          <Avatar size={24} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <span style={{ color: '#1890ff', fontSize: '16px', flex: 1 }}>{userInfo.name}</span>
          <RightOutlined style={{ fontSize: '14px', color: '#999' }} />
        </div>
      </div>
    </div>
  )
}

export default Sidebar
