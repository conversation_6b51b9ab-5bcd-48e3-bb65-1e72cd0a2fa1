import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![vertical-align-middle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1OS45IDQ3NEgxNjQuMWMtNC41IDAtOC4xIDMuNi04LjEgOHY2MGMwIDQuNCAzLjYgOCA4LjEgOGg2OTUuOGM0LjUgMCA4LjEtMy42IDguMS04di02MGMwLTQuNC0zLjYtOC04LjEtOHptLTM1My42LTc0LjdjMi45IDMuNyA4LjUgMy43IDExLjMgMGwxMDAuOC0xMjcuNWMzLjctNC43LjQtMTEuNy01LjctMTEuN0g1NTBWMTA0YzAtNC40LTMuNi04LTgtOGgtNjBjLTQuNCAwLTggMy42LTggOHYxNTZoLTYyLjhjLTYgMC05LjQgNy01LjcgMTEuN2wxMDAuOCAxMjcuNnptMTEuNCAyMjUuNGE3LjE0IDcuMTQgMCAwMC0xMS4zIDBMNDA1LjYgNzUyLjNhNy4yMyA3LjIzIDAgMDA1LjcgMTEuN0g0NzR2MTU2YzAgNC40IDMuNiA4IDggOGg2MGM0LjQgMCA4LTMuNiA4LThWNzY0aDYyLjhjNiAwIDkuNC03IDUuNy0xMS43TDUxNy43IDYyNC43eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
