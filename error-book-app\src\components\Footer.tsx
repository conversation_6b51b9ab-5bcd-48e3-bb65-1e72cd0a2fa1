import React from 'react'

const Footer: React.FC = () => {
  return (
    <div style={{
      padding: '58px 0 20px 0',
      marginTop: '40px'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        fontSize: '15px',
        color: '#999999'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
          <span
            style={{
              cursor: 'pointer',
              fontSize: '15px',
              color: '#666666',
              transition: 'color 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#1890ff'}
            onMouseLeave={(e) => e.currentTarget.style.color = '#666666'}
          >
            关于错题本
          </span>
          <span
            style={{
              cursor: 'pointer',
              fontSize: '15px',
              color: '#999999',
              transition: 'color 0.2s ease',
              margin: '0 10px'
            }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#1890ff'}
            onMouseLeave={(e) => e.currentTarget.style.color = '#666666'}
          >
            版权说明和法律声明
          </span>
          <span
            style={{
              cursor: 'pointer',
              fontSize: '15px',
              color: '#999999',
              transition: 'color 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#1890ff'}
            onMouseLeave={(e) => e.currentTarget.style.color = '#666666'}
          >
            隐私政策
          </span>
        </div>
        <div style={{
          fontSize: '15px',
          color: '#999999',
          fontWeight: '400'
        }}>
          © 1998-2025 Tencent Inc. All Rights Reserved.
        </div>
      </div>
    </div>
  )
}

export default Footer
