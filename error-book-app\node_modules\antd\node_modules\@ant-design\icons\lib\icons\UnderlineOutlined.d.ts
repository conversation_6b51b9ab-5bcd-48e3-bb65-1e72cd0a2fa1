import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![underline](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgyNCA4MDRIMjAwYy00LjQgMC04IDMuNC04IDcuNnY2MC44YzAgNC4yIDMuNiA3LjYgOCA3LjZoNjI0YzQuNCAwIDgtMy40IDgtNy42di02MC44YzAtNC4yLTMuNi03LjYtOC03LjZ6bS0zMTItNzZjNjkuNCAwIDEzNC42LTI3LjEgMTgzLjgtNzYuMkM3NDUgNjAyLjcgNzcyIDUzNy40IDc3MiA0NjhWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA5Ny03OSAxNzYtMTc2IDE3NnMtMTc2LTc5LTE3Ni0xNzZWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA2OS40IDI3LjEgMTM0LjYgNzYuMiAxODMuOEMzNzcuMyA3MDEgNDQyLjYgNzI4IDUxMiA3Mjh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
