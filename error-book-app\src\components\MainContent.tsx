import React, { useEffect } from 'react'
import { Card, Button, Avatar, Tag, Space } from 'antd'
import {
  PlusOutlined,
  HeartOutlined,
  MessageOutlined,
  ShareAltOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { useErrorBookStore } from '../store'

const MainContent: React.FC = () => {
  const {
    questions,
    statistics,
    updateStatistics
  } = useErrorBookStore()

  // 组件加载时更新统计数据
  useEffect(() => {
    updateStatistics()
  }, [updateStatistics])

  // 统计数据
  const todayStats = [
    { label: '新增错题', value: statistics.todayAdded, color: '#1890ff' },
    { label: '待复习', value: statistics.reviewingQuestions, color: '#ff6b35' },
    { label: '已掌握', value: statistics.masteredQuestions, color: '#52c41a' },
    { label: '总题数', value: statistics.totalQuestions, color: '#722ed1' }
  ]

  // 格式化时间显示
  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffTime = now.getTime() - new Date(date).getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays === 2) return '2天前'
    return `${diffDays}天前`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'reviewing': return '#ff6b35'
      case 'mastered': return '#52c41a'
      case 'difficult': return '#ff4d4f'
      case 'new': return '#1890ff'
      default: return '#666'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'reviewing': return '待复习'
      case 'mastered': return '已掌握'
      case 'difficult': return '困难'
      case 'new': return '新题目'
      default: return status
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#52c41a'
      case 'medium': return '#1890ff'
      case 'hard': return '#ff4d4f'
      default: return '#666'
    }
  }

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单'
      case 'medium': return '中等'
      case 'hard': return '困难'
      default: return difficulty
    }
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 数据统计卡片 */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '16px'
        }}>
          <span style={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
            今日数据
          </span>
          <InfoCircleOutlined style={{ color: '#999', fontSize: '14px' }} />
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '16px'
        }}>
          {todayStats.map((stat, index) => (
            <Card
              key={index}
              style={{
                textAlign: 'center',
                borderRadius: '8px',
                border: '1px solid #f0f0f0'
              }}
              styles={{ body: { padding: '20px 16px' } }}
            >
              <div style={{ color: '#999', fontSize: '14px', marginBottom: '8px' }}>
                {stat.label}
              </div>
              <div style={{
                fontSize: '28px',
                fontWeight: 'bold',
                color: stat.color
              }}>
                {stat.value}
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* 内容切换和操作按钮 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px'
      }}>
        <div style={{ display: 'flex', gap: '24px' }}>
          <span style={{
            fontSize: '16px',
            color: '#333',
            borderBottom: '2px solid #ff6b35',
            paddingBottom: '4px',
            cursor: 'pointer'
          }}>
            最近错题
          </span>
          <span style={{
            fontSize: '16px',
            color: '#999',
            cursor: 'pointer'
          }}>
            我的复习
          </span>
        </div>

        <Button
          type="primary"
          icon={<PlusOutlined />}
          style={{
            background: '#ff6b35',
            borderColor: '#ff6b35',
            borderRadius: '6px'
          }}
        >
          添加错题
        </Button>
      </div>

      {/* 错题列表 */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {questions.slice(0, 10).map((question) => (
          <Card
            key={question.id}
            style={{
              borderRadius: '8px',
              border: '1px solid #f0f0f0'
            }}
            styles={{ body: { padding: '20px' } }}
          >
            <div style={{ display: 'flex', gap: '16px' }}>
              <Avatar
                size={48}
                style={{
                  flexShrink: 0,
                  backgroundColor: getDifficultyColor(question.difficulty),
                  fontSize: '18px'
                }}
              >
                {question.subject.charAt(0)}
              </Avatar>

              <div style={{ flex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '8px'
                }}>
                  <Tag color={getDifficultyColor(question.difficulty)}>
                    {question.subject}
                  </Tag>
                  <span style={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
                    {question.title}
                  </span>
                  <Tag color={getStatusColor(question.status)} style={{ marginLeft: 'auto' }}>
                    {getStatusText(question.status)}
                  </Tag>
                </div>

                <div style={{
                  color: '#666',
                  fontSize: '14px',
                  lineHeight: '1.5',
                  marginBottom: '12px'
                }}>
                  {question.question.length > 100
                    ? question.question.substring(0, 100) + '...'
                    : question.question
                  }
                </div>

                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {question.tags.slice(0, 3).map((tag, index) => (
                      <Tag key={index} style={{ margin: 0 }}>
                        {tag}
                      </Tag>
                    ))}
                    {question.tags.length > 3 && (
                      <Tag style={{ margin: 0, color: '#999' }}>
                        +{question.tags.length - 3}
                      </Tag>
                    )}
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                    <span style={{ color: '#999', fontSize: '12px' }}>
                      {formatTimeAgo(question.createdAt)}
                    </span>
                    <Space size={16}>
                      <span style={{ color: '#999', cursor: 'pointer' }}>
                        <HeartOutlined /> 收藏
                      </span>
                      <span style={{ color: '#999', cursor: 'pointer' }}>
                        <MessageOutlined /> 笔记
                      </span>
                      <span style={{ color: '#999', cursor: 'pointer' }}>
                        <ShareAltOutlined /> 分享
                      </span>
                    </Space>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}

export default MainContent
