import React, { useEffect } from 'react'
import { Card, Button, Avatar, Tag, Space } from 'antd'
import {
  PlusOutlined,
  HeartOutlined,
  MessageOutlined,
  ShareAltOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { useErrorBookStore } from '../store'

const MainContent: React.FC = () => {
  const {
    questions,
    statistics,
    updateStatistics,
    setCurrentPage
  } = useErrorBookStore()

  // 组件加载时更新统计数据
  useEffect(() => {
    updateStatistics()
  }, [updateStatistics])

  // 统计数据
  const todayStats = [
    { label: '新增错题', value: statistics.todayAdded, color: '#1890ff' },
    { label: '待复习', value: statistics.reviewingQuestions, color: '#ff6b35' },
    { label: '已掌握', value: statistics.masteredQuestions, color: '#52c41a' },
    { label: '总题数', value: statistics.totalQuestions, color: '#722ed1' }
  ]

  // 格式化时间显示
  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffTime = now.getTime() - new Date(date).getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays === 2) return '2天前'
    return `${diffDays}天前`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'reviewing': return '#ff6b35'
      case 'mastered': return '#52c41a'
      case 'difficult': return '#ff4d4f'
      case 'new': return '#1890ff'
      default: return '#666'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'reviewing': return '待复习'
      case 'mastered': return '已掌握'
      case 'difficult': return '困难'
      case 'new': return '新题目'
      default: return status
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#52c41a'
      case 'medium': return '#1890ff'
      case 'hard': return '#ff4d4f'
      default: return '#666'
    }
  }

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单'
      case 'medium': return '中等'
      case 'hard': return '困难'
      default: return difficulty
    }
  }

  return (
    <div className="main-content-container">
      <div className="main-content-wrapper">
        {/* 数据统计卡片 */}
        <div style={{ marginBottom: '40px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            marginBottom: '16px'
          }}>
            <span style={{ fontSize: '16px', fontWeight: '500', color: '#333' }}>
              今日数据
            </span>
            <InfoCircleOutlined style={{ color: '#999', fontSize: '14px' }} />
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(220px, 1fr))',
            gap: '24px'
          }}>
            {todayStats.map((stat, index) => (
              <Card
                key={index}
                style={{
                  textAlign: 'center',
                  borderRadius: '12px',
                  border: 'none',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                  transition: 'all 0.3s ease'
                }}
                styles={{ body: { padding: '28px 20px' } }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)'
                  e.currentTarget.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.12)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)'
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.06)'
                }}
              >
                <div style={{
                  color: '#8c8c8c',
                  fontSize: '13px',
                  marginBottom: '12px',
                  fontWeight: '500',
                  letterSpacing: '0.5px'
                }}>
                  {stat.label}
                </div>
                <div style={{
                  fontSize: '32px',
                  fontWeight: '700',
                  color: stat.color,
                  lineHeight: '1.2'
                }}>
                  {stat.value}
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* 内容切换和操作按钮 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '32px',
          padding: '0 4px'
        }}>
          <div style={{ display: 'flex', gap: '24px' }}>
            <span style={{
              fontSize: '16px',
              color: '#333',
              borderBottom: '2px solid #ff6b35',
              paddingBottom: '4px',
              cursor: 'pointer'
            }}>
              最近错题
            </span>
            <span style={{
              fontSize: '16px',
              color: '#999',
              cursor: 'pointer'
            }}>
              我的复习
            </span>
          </div>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCurrentPage('add-error')}
            style={{
              background: '#ff6b35',
              borderColor: '#ff6b35',
              borderRadius: '6px'
            }}
          >
            添加错题
          </Button>
        </div>

        {/* 错题列表 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {questions.slice(0, 10).map((question) => (
            <Card
              key={question.id}
              style={{
                borderRadius: '16px',
                border: 'none',
                boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
                transition: 'all 0.3s ease',
                background: '#ffffff'
              }}
              styles={{ body: { padding: '32px' } }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.12)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0 2px 12px rgba(0, 0, 0, 0.08)'
              }}
            >
              <div style={{ display: 'flex', gap: '20px' }}>
                <Avatar
                  size={56}
                  style={{
                    flexShrink: 0,
                    backgroundColor: getDifficultyColor(question.difficulty),
                    fontSize: '20px',
                    fontWeight: '600'
                  }}
                >
                  {question.subject.charAt(0)}
                </Avatar>

                <div style={{ flex: 1 }}>
                  {/* 标题区域 */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '16px',
                    marginBottom: '16px'
                  }}>
                    <Tag
                      color={getDifficultyColor(question.difficulty)}
                      style={{
                        borderRadius: '6px',
                        fontSize: '12px',
                        fontWeight: '500',
                        padding: '4px 8px'
                      }}
                    >
                      {question.subject}
                    </Tag>
                    <span style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#262626',
                      lineHeight: '1.4'
                    }}>
                      {question.title}
                    </span>
                    <Tag
                      color={getStatusColor(question.status)}
                      style={{
                        marginLeft: 'auto',
                        borderRadius: '6px',
                        fontSize: '12px',
                        fontWeight: '500',
                        padding: '4px 8px'
                      }}
                    >
                      {getStatusText(question.status)}
                    </Tag>
                  </div>

                  {/* 问题内容 */}
                  <div style={{
                    color: '#595959',
                    fontSize: '15px',
                    lineHeight: '1.6',
                    marginBottom: '20px',
                    padding: '16px 0'
                  }}>
                    {question.question.length > 100
                      ? question.question.substring(0, 100) + '...'
                      : question.question
                    }
                  </div>

                  {/* 底部区域 */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingTop: '16px',
                    borderTop: '1px solid #f0f0f0'
                  }}>
                    <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                      {question.tags.slice(0, 3).map((tag, index) => (
                        <Tag
                          key={index}
                          style={{
                            margin: 0,
                            borderRadius: '4px',
                            fontSize: '11px',
                            color: '#8c8c8c',
                            backgroundColor: '#f5f5f5',
                            border: 'none'
                          }}
                        >
                          {tag}
                        </Tag>
                      ))}
                      {question.tags.length > 3 && (
                        <Tag style={{
                          margin: 0,
                          color: '#8c8c8c',
                          backgroundColor: '#f5f5f5',
                          border: 'none',
                          borderRadius: '4px',
                          fontSize: '11px'
                        }}>
                          +{question.tags.length - 3}
                        </Tag>
                      )}
                    </div>

                    <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
                      <span style={{
                        color: '#8c8c8c',
                        fontSize: '13px',
                        fontWeight: '500'
                      }}>
                        {formatTimeAgo(question.createdAt)}
                      </span>
                      <Space size={20}>
                        <span style={{
                          color: '#8c8c8c',
                          cursor: 'pointer',
                          fontSize: '13px',
                          fontWeight: '500',
                          transition: 'color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = '#ff6b35'}
                        onMouseLeave={(e) => e.currentTarget.style.color = '#8c8c8c'}
                        >
                          <HeartOutlined style={{ marginRight: '4px' }} />收藏
                        </span>
                        <span style={{
                          color: '#8c8c8c',
                          cursor: 'pointer',
                          fontSize: '13px',
                          fontWeight: '500',
                          transition: 'color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = '#ff6b35'}
                        onMouseLeave={(e) => e.currentTarget.style.color = '#8c8c8c'}
                        >
                          <MessageOutlined style={{ marginRight: '4px' }} />笔记
                        </span>
                        <span style={{
                          color: '#8c8c8c',
                          cursor: 'pointer',
                          fontSize: '13px',
                          fontWeight: '500',
                          transition: 'color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = '#ff6b35'}
                        onMouseLeave={(e) => e.currentTarget.style.color = '#8c8c8c'}
                        >
                          <ShareAltOutlined style={{ marginRight: '4px' }} />分享
                        </span>
                      </Space>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

export default MainContent
