import { create } from 'zustand'
import { ErrorQuestion, Statistics, UserInfo, Notification, AppState } from '../types'

interface ErrorBookStore {
  // 状态
  questions: ErrorQuestion[]
  statistics: Statistics
  userInfo: UserInfo
  notifications: Notification[]
  appState: AppState

  // 错题相关操作
  addQuestion: (question: Omit<ErrorQuestion, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateQuestion: (id: string, updates: Partial<ErrorQuestion>) => void
  deleteQuestion: (id: string) => void
  getQuestionsBySubject: (subject: string) => ErrorQuestion[]
  getQuestionsByStatus: (status: ErrorQuestion['status']) => ErrorQuestion[]

  // 复习相关操作
  markAsReviewed: (id: string, masteryLevel: number) => void
  getTodayReviewQuestions: () => ErrorQuestion[]

  // 统计相关操作
  updateStatistics: () => void

  // 通知相关操作
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void
  markNotificationAsRead: (id: string) => void
  getUnreadNotifications: () => Notification[]

  // 应用状态操作
  setCurrentPage: (page: string) => void
  setSearchKeyword: (keyword: string) => void
  setFilterOptions: (options: Partial<AppState['filterOptions']>) => void
}

// 生成ID的辅助函数
const generateId = () => Math.random().toString(36).substr(2, 9)

// 初始化数据
const initialStatistics: Statistics = {
  totalQuestions: 0,
  newQuestions: 0,
  reviewingQuestions: 0,
  masteredQuestions: 0,
  difficultQuestions: 0,
  todayAdded: 0,
  todayReviewed: 0,
  weeklyProgress: [0, 0, 0, 0, 0, 0, 0],
  subjectDistribution: []
}

const initialUserInfo: UserInfo = {
  id: 'user1',
  name: '学习小助手',
  level: 1,
  experience: 0,
  studyDays: 1,
  achievements: []
}

const initialAppState: AppState = {
  currentPage: 'home',
  searchKeyword: '',
  filterOptions: {}
}

// 模拟初始数据
const mockQuestions: ErrorQuestion[] = [
  {
    id: '1',
    subject: '数学',
    chapter: '二次函数',
    title: '二次函数的图像与性质',
    question: '已知二次函数f(x)=ax²+bx+c的图像过点(1,0)，且对称轴为x=2，求函数解析式...',
    answer: 'f(x) = x² - 4x + 3',
    myAnswer: 'f(x) = x² - 3x + 2',
    explanation: '根据对称轴公式 x = -b/(2a) = 2，可得 b = -4a。再利用过点(1,0)的条件...',
    difficulty: 'medium',
    tags: ['二次函数', '图像性质'],
    masteryLevel: 60,
    reviewCount: 2,
    lastReviewDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天
    nextReviewDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 明天
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
    updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    status: 'reviewing'
  },
  {
    id: '2',
    subject: '英语',
    chapter: '语法',
    title: '语法填空 - 时态语态',
    question: 'The book _____ (write) by the famous author last year has become very popular...',
    answer: 'written',
    myAnswer: 'wrote',
    explanation: '这里需要用过去分词作后置定语，修饰book。The book written by... = The book that was written by...',
    difficulty: 'easy',
    tags: ['时态', '语态', '语法填空'],
    masteryLevel: 85,
    reviewCount: 3,
    lastReviewDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    nextReviewDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    status: 'mastered'
  },
  {
    id: '3',
    subject: '物理',
    chapter: '力学',
    title: '牛顿第二定律应用',
    question: '质量为2kg的物体在水平面上受到10N的水平拉力，摩擦系数为0.2，求物体的加速度...',
    answer: 'a = 3 m/s²',
    myAnswer: 'a = 5 m/s²',
    explanation: '首先计算摩擦力 f = μmg = 0.2 × 2 × 10 = 4N，然后根据牛顿第二定律 F合 = ma，得到 (10-4) = 2a，所以 a = 3 m/s²',
    difficulty: 'hard',
    tags: ['牛顿定律', '力学'],
    masteryLevel: 30,
    reviewCount: 1,
    lastReviewDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
    nextReviewDate: new Date(),
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    status: 'difficult'
  }
]

export const useErrorBookStore = create<ErrorBookStore>((set, get) => ({
      // 初始状态
      questions: mockQuestions,
      statistics: initialStatistics,
      userInfo: initialUserInfo,
      notifications: [
        {
          id: '1',
          type: 'review',
          title: '复习提醒',
          content: '您有3道题需要复习',
          read: false,
          createdAt: new Date()
        }
      ],
      appState: initialAppState,

      // 错题操作
      addQuestion: (questionData) => {
        const newQuestion: ErrorQuestion = {
          ...questionData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
          masteryLevel: 0,
          reviewCount: 0,
          lastReviewDate: new Date(),
          nextReviewDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          status: 'new'
        }

        set((state) => ({
          questions: [...state.questions, newQuestion]
        }))

        // 更新统计数据
        get().updateStatistics()
      },

      updateQuestion: (id, updates) => {
        set((state) => ({
          questions: state.questions.map(q =>
            q.id === id ? { ...q, ...updates, updatedAt: new Date() } : q
          )
        }))
        get().updateStatistics()
      },

      deleteQuestion: (id) => {
        set((state) => ({
          questions: state.questions.filter(q => q.id !== id)
        }))
        get().updateStatistics()
      },

      getQuestionsBySubject: (subject) => {
        return get().questions.filter(q => q.subject === subject)
      },

      getQuestionsByStatus: (status) => {
        return get().questions.filter(q => q.status === status)
      },

      // 复习操作
      markAsReviewed: (id, masteryLevel) => {
        const question = get().questions.find(q => q.id === id)
        if (!question) return

        let newStatus: ErrorQuestion['status'] = 'reviewing'
        if (masteryLevel >= 80) newStatus = 'mastered'
        else if (masteryLevel < 40) newStatus = 'difficult'

        // 计算下次复习时间
        let nextReviewDays = 1
        if (masteryLevel >= 80) nextReviewDays = 7
        else if (masteryLevel >= 60) nextReviewDays = 3
        else if (masteryLevel >= 40) nextReviewDays = 1

        get().updateQuestion(id, {
          masteryLevel,
          status: newStatus,
          reviewCount: question.reviewCount + 1,
          lastReviewDate: new Date(),
          nextReviewDate: new Date(Date.now() + nextReviewDays * 24 * 60 * 60 * 1000)
        })
      },

      getTodayReviewQuestions: () => {
        const today = new Date()
        today.setHours(23, 59, 59, 999)

        return get().questions.filter(q =>
          q.nextReviewDate <= today && q.status !== 'mastered'
        )
      },

      // 统计操作
      updateStatistics: () => {
        const questions = get().questions
        const today = new Date()
        today.setHours(0, 0, 0, 0)

        const todayAdded = questions.filter(q => {
          const createdDate = new Date(q.createdAt)
          createdDate.setHours(0, 0, 0, 0)
          return createdDate.getTime() === today.getTime()
        }).length

        const subjectDistribution = questions.reduce((acc, q) => {
          const existing = acc.find(item => item.subject === q.subject)
          if (existing) {
            existing.count++
          } else {
            acc.push({ subject: q.subject, count: 1 })
          }
          return acc
        }, [] as { subject: string; count: number }[])

        set((state) => ({
          statistics: {
            ...state.statistics,
            totalQuestions: questions.length,
            newQuestions: questions.filter(q => q.status === 'new').length,
            reviewingQuestions: questions.filter(q => q.status === 'reviewing').length,
            masteredQuestions: questions.filter(q => q.status === 'mastered').length,
            difficultQuestions: questions.filter(q => q.status === 'difficult').length,
            todayAdded,
            subjectDistribution
          }
        }))
      },

      // 通知操作
      addNotification: (notificationData) => {
        const newNotification: Notification = {
          ...notificationData,
          id: generateId(),
          createdAt: new Date()
        }

        set((state) => ({
          notifications: [newNotification, ...state.notifications]
        }))
      },

      markNotificationAsRead: (id) => {
        set((state) => ({
          notifications: state.notifications.map(n =>
            n.id === id ? { ...n, read: true } : n
          )
        }))
      },

      getUnreadNotifications: () => {
        return get().notifications.filter(n => !n.read)
      },

      // 应用状态操作
      setCurrentPage: (page) => {
        set((state) => ({
          appState: { ...state.appState, currentPage: page }
        }))
      },

      setSearchKeyword: (keyword) => {
        set((state) => ({
          appState: { ...state.appState, searchKeyword: keyword }
        }))
      },

      setFilterOptions: (options) => {
        set((state) => ({
          appState: {
            ...state.appState,
            filterOptions: { ...state.appState.filterOptions, ...options }
          }
        }))
      }
    }))
